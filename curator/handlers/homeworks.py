from aiogram import Router, F
from aiogram.types import CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from ..keyboards.main import get_curator_main_menu_kb
from common.keyboards import get_main_menu_back_button
from ..keyboards.homeworks import get_homework_menu_kb
from common.analytics.keyboards import get_groups_for_analytics_kb
from database import CuratorRepository, UserRepository, GroupRepository, StudentRepository

class CuratorHomeworkStates(StatesGroup):
    homework_menu = State()
    student_stats_group = State()
    student_stats_list = State()
    group_stats_group = State()
    group_stats_result = State()


router = Router()

@router.callback_query(F.data == "curator_homeworks")
async def show_homework_menu(callback: CallbackQuery, state: FSMContext):
    """Показать меню домашних заданий"""
    await callback.message.edit_text(
        "Выберите тип статистики:",
        reply_markup=get_homework_menu_kb()
    )
    await state.set_state(CuratorHomeworkStates.homework_menu)

# Обработчики для статистики по ученику
@router.callback_query(CuratorHomeworkStates.homework_menu, F.data == "hw_student_stats")
async def select_student_stats_group(callback: CallbackQuery, state: FSMContext):
    """Выбор группы для статистики по ученику"""
    # Получаем группы куратора
    groups_kb = await get_groups_for_analytics_kb("curator", callback.from_user.id)

    await callback.message.edit_text(
        "Выберите группу:",
        reply_markup=groups_kb
    )
    await state.set_state(CuratorHomeworkStates.student_stats_group)

@router.callback_query(CuratorHomeworkStates.student_stats_group, F.data.startswith("analytics_group_"))
async def show_student_stats_list(callback: CallbackQuery, state: FSMContext):
    """Показать список учеников с выполненными и невыполненными ДЗ"""
    group_id = int(callback.data.replace("analytics_group_", ""))
    await state.update_data(selected_group=group_id)

    # Получаем информацию о группе
    group = await GroupRepository.get_by_id(group_id)
    if not group:
        await callback.message.edit_text(
            "❌ Группа не найдена",
            reply_markup=InlineKeyboardMarkup(inline_keyboard=get_main_menu_back_button())
        )
        return

    # Получаем студентов группы
    students = await StudentRepository.get_by_group(group_id)

    # Формируем список студентов с их статусом выполнения ДЗ
    completed_students = []
    not_completed_students = []

    for student in students:
        # Здесь должна быть логика проверки выполнения ДЗ
        # Пока используем заглушку
        if student.id % 2 == 0:  # Заглушка: четные ID выполнили
            completed_students.append(student)
        else:
            not_completed_students.append(student)
    
    # Формируем текст с результатами
    text = f"📊 Статистика выполнения ДЗ\n"
    text += f"👥 Группа: {group.name}"
    if group.subject:
        text += f" ({group.subject.name})"
    text += "\n\n"

    # Добавляем информацию о выполнивших
    if completed_students:
        text += "✅ Выполнили:\n"
        for i, student in enumerate(completed_students, 1):
            text += f"{i}. {student.user.name}\n"
        text += "\n"

    # Добавляем информацию о не выполнивших
    if not_completed_students:
        text += "❌ Не выполнили:\n"
        for i, student in enumerate(not_completed_students, 1):
            # Создаем ссылку на Telegram профиль
            text += f"{i}. [{student.user.name}](tg://user?id={student.user.telegram_id})\n"
        text += "\n"

    if not completed_students and not not_completed_students:
        text += "❌ Студенты в группе не найдены"

    await callback.message.edit_text(
        text,
        reply_markup=InlineKeyboardMarkup(inline_keyboard=get_main_menu_back_button()),
        parse_mode="Markdown"
    )
    await state.set_state(CuratorHomeworkStates.student_stats_list)

# Обработчики для статистики по группе
@router.callback_query(CuratorHomeworkStates.homework_menu, F.data == "hw_group_stats")
async def select_group_stats_group(callback: CallbackQuery, state: FSMContext):
    """Выбор группы для статистики по группе"""
    # Получаем группы куратора
    groups_kb = await get_groups_for_analytics_kb("curator", callback.from_user.id)

    await callback.message.edit_text(
        "Выберите группу для просмотра статистики:",
        reply_markup=groups_kb
    )
    await state.set_state(CuratorHomeworkStates.group_stats_group)

@router.callback_query(CuratorHomeworkStates.group_stats_group, F.data.startswith("analytics_group_"))
async def show_group_stats(callback: CallbackQuery, state: FSMContext):
    """Показать статистику по группе"""
    group_id = int(callback.data.replace("analytics_group_", ""))

    # Получаем информацию о группе
    group = await GroupRepository.get_by_id(group_id)
    if not group:
        await callback.message.edit_text(
            "❌ Группа не найдена",
            reply_markup=InlineKeyboardMarkup(inline_keyboard=get_main_menu_back_button())
        )
        return

    # Получаем студентов группы
    students = await StudentRepository.get_by_group(group_id)

    if not students:
        await callback.message.edit_text(
            f"👥 Группа: {group.name}\n"
            f"❌ Студенты в группе не найдены",
            reply_markup=InlineKeyboardMarkup(inline_keyboard=get_main_menu_back_button())
        )
        return

    # Вычисляем статистику (заглушка)
    # В реальном приложении здесь будет расчет процента выполнения ДЗ
    total_students = len(students)
    completed_count = sum(1 for student in students if student.id % 2 == 0)  # Заглушка
    avg_completion = (completed_count / total_students * 100) if total_students > 0 else 0

    # Находим отстающих студентов (заглушка - студенты с нечетными ID)
    lagging_students = [student for student in students if student.id % 2 == 1]

    # Формируем текст результата
    text = f"📊 Статистика по группе\n"
    text += f"👥 Группа: {group.name}"
    if group.subject:
        text += f" ({group.subject.name})"
    text += f"\n\n📈 Средний % выполнения: {avg_completion:.1f}%\n"
    text += f"(формула: % выполнения ДЗ = (выполнено единожды / все дз курса) * 100% и после сумма всех процентов и деление на количество учеников в группе)\n\n"

    # Добавляем список отстающих
    if lagging_students:
        text += "📋 Список отстающих: ученики, не выполняющие 5 дней подряд ДЗ\n"
        for i, student in enumerate(lagging_students, 1):
            text += f"{i}. [{student.user.name}](tg://user?id={student.user.telegram_id})\n"
    else:
        text += "✅ Отстающих студентов нет"

    await callback.message.edit_text(
        text,
        reply_markup=InlineKeyboardMarkup(inline_keyboard=get_main_menu_back_button()),
        parse_mode="Markdown"
    )
    await state.set_state(CuratorHomeworkStates.group_stats_result)

